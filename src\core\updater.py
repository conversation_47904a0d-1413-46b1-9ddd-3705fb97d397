"""
Auto-updater module for Agevolami PM
Handles checking for updates and downloading new versions from GitHub releases
"""

import json
import os
import sys
import tempfile
import zipfile
from pathlib import Path
from typing import Optional, Dict, Any
import urllib.request
import urllib.error
from packaging import version
import subprocess
import shutil

class AutoUpdater:
    """<PERSON>les automatic updates from GitHub releases"""
    
    def __init__(self, 
                 repo_owner: str = "serhabdel",  # Updated to your username
                 repo_name: str = "agevolami_pm_v2",
                 current_version: str = "2.3.3",
                 github_token: str = None):
        self.repo_owner = repo_owner
        self.repo_name = repo_name
        self.current_version = current_version
        self.github_token = github_token
        self.github_api_url = f"https://api.github.com/repos/{repo_owner}/{repo_name}"
        self.update_check_file = Path("data/last_update_check.json")
        
        # Load token from config file if not provided
        if not self.github_token:
            self.github_token = self._load_github_token()
    
    def _load_github_token(self) -> Optional[str]:
        """Load GitHub token from config file"""
        try:
            token_file = Path("data/config/github_token.txt")
            if token_file.exists():
                with open(token_file, 'r') as f:
                    token = f.read().strip()
                    if token:
                        return token
        except Exception as e:
            print(f"Error loading GitHub token: {e}")
        return None
    
    def save_github_token(self, token: str) -> bool:
        """Save GitHub token to config file"""
        try:
            token_file = Path("data/config/github_token.txt")
            token_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(token_file, 'w') as f:
                f.write(token.strip())
            
            self.github_token = token.strip()
            return True
        except Exception as e:
            print(f"Error saving GitHub token: {e}")
            return False
    
    def _create_authenticated_request(self, url: str) -> urllib.request.Request:
        """Create an authenticated request for GitHub API"""
        headers = {
            'User-Agent': 'Agevolami-PM-Updater/1.0',
            'Accept': 'application/vnd.github.v3+json'
        }
        
        if self.github_token:
            headers['Authorization'] = f'token {self.github_token}'
        
        return urllib.request.Request(url, headers=headers)
        
    def check_for_updates(self, force_check: bool = False) -> Optional[Dict[str, Any]]:
        """
        Check if a new version is available
        
        Args:
            force_check: Skip time-based checking and force immediate check
            
        Returns:
            Dict with update info if available, None otherwise
        """
        try:
            # Check if we should skip based on last check time
            if not force_check and not self._should_check_for_updates():
                return None
                
            # Get latest release info from GitHub
            latest_release = self._get_latest_release()
            if not latest_release:
                return None
                
            latest_version = latest_release.get('tag_name', '').lstrip('v')
            
            # Compare versions
            if self._is_newer_version(latest_version, self.current_version):
                update_info = {
                    'available': True,
                    'latest_version': latest_version,
                    'current_version': self.current_version,
                    'download_url': self._get_download_url(latest_release),
                    'release_notes': latest_release.get('body', ''),
                    'release_date': latest_release.get('published_at', ''),
                    'size': self._get_asset_size(latest_release)
                }
                
                # Save last check time
                self._save_last_check()
                return update_info
                
            # Save last check time even if no update
            self._save_last_check()
            return {'available': False}
            
        except Exception as e:
            print(f"Error checking for updates: {e}")
            return None

    def download_and_install_update(self, download_url: str) -> bool:
        """
        Download and install the update
        
        Args:
            download_url: URL to download the update from
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Create temp directory for download
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_path = Path(temp_dir)
                zip_file = temp_path / "update.zip"
                
                # Download the update with authentication
                print("Downloading update...")
                self._download_authenticated_file(download_url, zip_file)
                
                # Extract the update
                extract_path = temp_path / "extracted"
                extract_path.mkdir()
                
                with zipfile.ZipFile(zip_file, 'r') as zip_ref:
                    zip_ref.extractall(extract_path)
                
                # Install the update
                return self._install_update(extract_path)
                
        except Exception as e:
            print(f"Error downloading/installing update: {e}")
            return False
    
    def _download_authenticated_file(self, url: str, destination: Path):
        """Download a file from GitHub with authentication"""
        request = self._create_authenticated_request(url)
        
        with urllib.request.urlopen(request) as response:
            with open(destination, 'wb') as f:
                shutil.copyfileobj(response, f)

    def _should_check_for_updates(self) -> bool:
        """Check if enough time has passed since last update check"""
        try:
            if not self.update_check_file.exists():
                return True
                
            with open(self.update_check_file, 'r') as f:
                data = json.load(f)
                
            import time
            last_check = data.get('last_check', 0)
            # Check once per day (86400 seconds)
            return (time.time() - last_check) > 86400
            
        except:
            return True
    
    def _save_last_check(self):
        """Save the timestamp of the last update check"""
        try:
            import time
            data = {'last_check': time.time()}
            
            # Ensure data directory exists
            self.update_check_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(self.update_check_file, 'w') as f:
                json.dump(data, f)
        except Exception as e:
            print(f"Error saving last check time: {e}")
    
    def _get_latest_release(self) -> Optional[Dict[str, Any]]:
        """Get latest release info from GitHub API"""
        try:
            url = f"{self.github_api_url}/releases/latest"
            request = self._create_authenticated_request(url)
            
            with urllib.request.urlopen(request) as response:
                return json.loads(response.read().decode())
        except urllib.error.HTTPError as e:
            if e.code == 404:
                print("No releases found or repository not accessible")
            elif e.code == 401:
                print("Authentication failed - check GitHub token")
            else:
                print(f"HTTP Error {e.code}: {e.reason}")
            return None
        except Exception as e:
            print(f"Error fetching latest release: {e}")
            return None
    
    def test_authentication(self) -> bool:
        """Test if GitHub authentication is working"""
        try:
            url = f"https://api.github.com/repos/{self.repo_owner}/{self.repo_name}"
            request = self._create_authenticated_request(url)
            
            with urllib.request.urlopen(request) as response:
                data = json.loads(response.read().decode())
                return data.get('private', False) == True  # Should be True for private repo
        except Exception as e:
            print(f"Authentication test failed: {e}")
            return False

    def _is_newer_version(self, latest: str, current: str) -> bool:
        """Compare version strings"""
        try:
            return version.parse(latest) > version.parse(current)
        except:
            # Fallback to string comparison
            return latest != current
    
    def _get_download_url(self, release_data: Dict[str, Any]) -> Optional[str]:
        """Extract download URL for Windows build from release data"""
        assets = release_data.get('assets', [])
        for asset in assets:
            if 'Windows.zip' in asset.get('name', ''):
                return asset.get('browser_download_url')
        return None
    
    def _get_asset_size(self, release_data: Dict[str, Any]) -> int:
        """Get size of the Windows asset in bytes"""
        assets = release_data.get('assets', [])
        for asset in assets:
            if 'Windows.zip' in asset.get('name', ''):
                return asset.get('size', 0)
        return 0
    
    def _install_update(self, extract_path: Path) -> bool:
        """Install the extracted update"""
        try:
            # Get current executable directory
            if getattr(sys, 'frozen', False):
                # Running as compiled executable
                current_dir = Path(sys.executable).parent
            else:
                # Running as script
                current_dir = Path(__file__).parent.parent.parent
            
            # Create backup of current installation
            backup_dir = current_dir.parent / f"backup_{self.current_version}"
            if backup_dir.exists():
                shutil.rmtree(backup_dir)
            shutil.copytree(current_dir, backup_dir)
            
            # Copy new files
            for item in extract_path.rglob('*'):
                if item.is_file():
                    relative_path = item.relative_to(extract_path)
                    target_path = current_dir / relative_path
                    target_path.parent.mkdir(parents=True, exist_ok=True)
                    shutil.copy2(item, target_path)
            
            # Create restart script
            restart_script = current_dir / "restart_after_update.bat"
            with open(restart_script, 'w') as f:
                f.write(f"""@echo off
timeout /t 2 /nobreak > nul
start "" "{current_dir / 'AgevolamiPM.exe'}"
del "%~f0"
""")
            
            return True
            
        except Exception as e:
            print(f"Error installing update: {e}")
            return False
    
    def restart_application(self):
        """Restart the application after update"""
        try:
            if getattr(sys, 'frozen', False):
                # Running as executable
                restart_script = Path(sys.executable).parent / "restart_after_update.bat"
                subprocess.Popen([str(restart_script)], shell=True)
                sys.exit(0)
            else:
                # Running as script - just exit, user needs to restart manually
                print("Please restart the application to complete the update.")
                sys.exit(0)
        except Exception as e:
            print(f"Error restarting application: {e}")
