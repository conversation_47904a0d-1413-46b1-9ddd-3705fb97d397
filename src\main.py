#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Agevolami PM - Sistema di Gestione Progetti e Scadenze
Entry point principale dell'applicazione

Autore: Agevolami Development Team
Data: 2025
Versione: 1.0.0
"""

import flet as ft
import sys
import os
from pathlib import Path

# Set UTF-8 encoding for Windows builds - Enhanced
if sys.platform == 'win32':
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    os.environ['PYTHONUTF8'] = '1'
    os.environ['PYTHONLEGACYWINDOWSSTDIO'] = '1'

    # Force console to UTF-8 if possible
    try:
        import subprocess
        subprocess.run(['chcp', '65001'], shell=True, capture_output=True, check=False)
    except:
        pass

    # Reconfigure stdout/stderr for UTF-8
    try:
        import io
        if hasattr(sys.stdout, 'buffer'):
            sys.stdout = io.TextIOWrapper(
                sys.stdout.buffer,
                encoding='utf-8',
                errors='replace',
                line_buffering=True
            )
        if hasattr(sys.stderr, 'buffer'):
            sys.stderr = io.TextIOWrapper(
                sys.stderr.buffer,
                encoding='utf-8',
                errors='replace',
                line_buffering=True
            )
    except:
        pass

# Aggiungi il percorso src al PYTHONPATH
src_path = Path(__file__).parent
sys.path.insert(0, str(src_path))

# Import delle funzionalità principali
from core import DatabaseManagerExtended, AppConfig, setup_logger, AlertService, SchedulerService
from core.utils.windows_utils import WindowsSystemIntegration
from ui.layout import MainLayout

# Setup logging
logger = setup_logger()

class AgevolamiApp:
    """
    Classe principale dell'applicazione Agevolami PM
    """
    
    def __init__(self):
        self.config = AppConfig()
        self.db_manager = DatabaseManagerExtended(self.config.database_path)
        
        # Inizializza integrazione Windows
        self.windows_integration = WindowsSystemIntegration("Agevolami PM")
        
        # Inizializza servizi con integrazione Windows
        self.alert_service = AlertService(self.db_manager, self.config, self.windows_integration)
        self.scheduler_service = SchedulerService(self.db_manager, self.config)

        # Inizializza servizio statistiche
        from core.services.statistics_service import StatisticsService
        self.statistics_service = StatisticsService(self.db_manager, self.config)

        # Initialize settings controller to ensure settings are loaded
        self._init_settings_controller()

        # Inizializza servizio documenti
        from core.services.document_service import DocumentService
        self.document_service = DocumentService(self.config.data_dir, self.db_manager)
        
        # Inizializza update manager
        self.update_manager = None
        
        self.main_layout = None
        self.page = None
        
    @property
    def db(self):
        """Accesso al database manager"""
        return self.db_manager
        
    def initialize_app(self, page: ft.Page):
        """
        Inizializza l'applicazione con configurazioni e layout
        """
        try:
            # Configurazione pagina principale
            page.title = "Agevolami PM - Gestione Progetti e Scadenze"
            page.window_icon = "assets/logo_v2.png"  # Imposta icona finestra
            page.window_width = 1400
            page.window_height = 900
            page.window_min_width = 1200
            page.window_min_height = 700
            page.theme_mode = ft.ThemeMode.LIGHT
            page.padding = 0
            page.spacing = 0
            
            # Tema personalizzato business
            page.theme = ft.Theme(
                color_scheme_seed=ft.Colors.BLUE_800,
                visual_density=ft.VisualDensity.COMFORTABLE
            )
            
            # Database già inizializzato nel costruttore
            logger.info("Database inizializzato correttamente")
            
            # Imposta il riferimento alla pagina
            self.page = page
            
            # Crea layout principale
            logger.info("Creating main layout...")
            self.main_layout = MainLayout(page, self)
            
            # Costruisce e aggiungi layout alla pagina
            logger.info("Building layout content...")
            layout_content = self.main_layout.build()
            
            logger.info("Adding layout to page...")
            page.add(layout_content)
            
            # Force initial view load
            logger.info("Loading initial dashboard view...")
            self.main_layout._navigate_to("dashboard", add_to_history=False)
            
            # Aggiorna pagina
            logger.info("Updating page...")
            page.update()
            
            logger.info("Layout initialization complete!")
            
            # Avvia il servizio di scheduling per i report automatici
            self.scheduler_service.start()
            
            # Inizializza update manager
            try:
                from core.update_manager import UpdateManager
                self.update_manager = UpdateManager(page, "2.0.10")  # TODO: Read version from config
                # Check for updates on startup (silent)
                self.update_manager.check_for_updates_on_startup(silent=True)
            except Exception as e:
                logger.warning(f"Update manager initialization failed: {e}")
            
            # Verifica e invia notifica di avvio se abilitata
            self._send_startup_notification()
            
            logger.info("Applicazione Agevolami PM avviata correttamente")
            
        except Exception as e:
            logger.error(f"Errore durante l'inizializzazione: {e}")
            # Mostra dialog di errore
            error_dialog = ft.AlertDialog(
                title=ft.Text("Errore di Inizializzazione"),
                content=ft.Text(f"Si è verificato un errore durante l'avvio dell'applicazione:\n\n{str(e)}"),
                actions=[
                    ft.TextButton(
                        "Chiudi",
                        on_click=lambda _: page.window_close()
                    )
                ]
            )
            page.open(error_dialog)
    
    def _send_startup_notification(self):
        """Invia notifica di avvio se abilitata"""
        try:
            windows_config = getattr(self.config, 'windows_config', {})
            if not windows_config.get('notifications_enabled', True):
                return
            
            # Invia notifica di avvio solo se non è in orari silenziosi
            if not self._is_quiet_hours():
                self.windows_integration.send_notification(
                    "Agevolami PM",
                    "Applicazione avviata correttamente!\nSistema di gestione progetti e scadenze attivo.",
                    "low"
                )
        except Exception as e:
            logger.error(f"Errore notifica avvio: {e}")
    
    def _is_quiet_hours(self) -> bool:
        """Verifica se siamo negli orari silenziosi per le notifiche di sistema"""
        try:
            windows_config = getattr(self.config, 'windows_config', {})
            if not windows_config.get('notification_quiet_hours_enabled', False):
                return False
            
            from datetime import datetime
            now = datetime.now().time()
            start_time_str = windows_config.get('notification_quiet_start', '22:00')
            end_time_str = windows_config.get('notification_quiet_end', '08:00')
            
            # Parse dei tempi
            start_time = datetime.strptime(start_time_str, '%H:%M').time()
            end_time = datetime.strptime(end_time_str, '%H:%M').time()
            
            # Gestisce il caso in cui l'orario attraversa la mezzanotte
            if start_time <= end_time:
                return start_time <= now <= end_time
            else:
                return now >= start_time or now <= end_time
                
        except Exception as e:
            logger.error(f"Errore controllo orari silenziosi: {e}")
            return False

    def _init_settings_controller(self):
        """Initialize settings controller to ensure settings persistence"""
        try:
            # The new simplified settings system doesn't need a separate controller
            # Settings are managed directly by the SettingsView
            logger.info("Using simplified settings system - no separate controller needed")
            self.settings_controller = None

        except Exception as e:
            logger.error(f"Error initializing settings: {e}")
            self.settings_controller = None

def main(page: ft.Page):
    """
    Funzione principale di entry point per Flet
    """
    app = AgevolamiApp()
    app.initialize_app(page)

if __name__ == "__main__":
    # Avvia l'applicazione Flet
    ft.app(
        target=main,
        name="Agevolami PM",
        assets_dir="assets"
    )